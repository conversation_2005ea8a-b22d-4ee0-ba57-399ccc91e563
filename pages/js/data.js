// 数据资产配置数据
const DATA_CONFIG = {


    // 数据资产分类
    assetCategories: [
        {
            id: 'database',
            name: '数据库资产',
            icon: 'fas fa-database',
            description: 'MySQL、PostgreSQL等关系型数据库资产',
            color: '#4285f4',
            count: 10400
        },
        {
            id: 'report',
            name: '报表资产',
            icon: 'fas fa-chart-line',
            description: 'FineReport报表、业务分析报告等可视化资产',
            color: '#fd79a8',
            count: 1400
        },
        {
            id: 'bi',
            name: 'BI分析资产',
            icon: 'fas fa-chart-bar',
            description: 'FineBI仪表板、数据分析模型等商业智能资产',
            color: '#fdcb6e',
            count: 1100
        },
        {
            id: 'integration',
            name: '数据集成资产',
            icon: 'fas fa-link',
            description: 'FineDataLink数据管道、ETL任务等集成资产',
            color: '#ff6b6b',
            count: 1
        },
        {
            id: 'application',
            name: '应用资产',
            icon: 'fas fa-cloud',
            description: '简道云应用、无代码平台等应用资产',
            color: '#00d4aa',
            count: 1
        },
        {
            id: 'api',
            name: 'API资产',
            icon: 'fas fa-plug',
            description: '各平台提供的API接口、数据服务等',
            color: '#55a3ff',
            count: 89
        }
    ],

    // 数据资产域配置
    domains: [
        {
            id: 'cust',
            name: '客户域',
            icon: 'fas fa-users',
            description: '客户基础信息、客户关系管理、客户画像分析等核心客户数据资产',
            assetCount: 78,
            apiCount: 12,
            reportCount: 23,
            updateFreq: '实时',
            color: '#4285f4',
            subDomains: ['客户基础信息', '客户关系', '客户画像', '客户行为'],
            assetTypes: ['MySQL表', 'FineReport报表', 'FineBI仪表板', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        },
        {
            id: 'sales',
            name: '销售域',
            icon: 'fas fa-chart-line',
            description: '销售过程管理、商机跟踪、合同管理、项目实施等销售全流程数据资产',
            assetCount: 95,
            apiCount: 18,
            reportCount: 34,
            updateFreq: '每小时',
            color: '#fd79a8',
            subDomains: ['商机管理', '合同管理', '项目实施', '销售分析'],
            assetTypes: ['MySQL表', 'PostgreSQL表', 'FineReport报表', 'FineBI分析'],
            platforms: ['MySQL', 'PostgreSQL', 'FineReport', 'FineBI']
        },
        {
            id: 'fin',
            name: '财务域',
            icon: 'fas fa-coins',
            description: '财务核算、资金管理、成本控制、财务分析等财务管理数据资产',
            assetCount: 72,
            apiCount: 8,
            reportCount: 28,
            updateFreq: '每日',
            color: '#26de81',
            subDomains: ['财务核算', '资金管理', '成本分析', '预算管理'],
            assetTypes: ['MySQL表', 'FineReport财务报表', 'FineBI财务分析', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        },
        {
            id: 'oprn',
            name: '运营域',
            icon: 'fas fa-cogs',
            description: '运营活动管理、营销分析、用户运营、产品运营等运营数据资产',
            assetCount: 56,
            apiCount: 15,
            reportCount: 19,
            updateFreq: '每小时',
            color: '#fdcb6e',
            subDomains: ['营销运营', '用户运营', '产品运营', '数据运营'],
            assetTypes: ['PostgreSQL表', 'FineBI运营分析', '简道云应用', 'API接口'],
            platforms: ['PostgreSQL', 'FineBI', '简道云']
        },
        {
            id: 'hr',
            name: '人事域',
            icon: 'fas fa-user-tie',
            description: '人员组织架构、绩效考核、薪酬管理、人才发展等人力资源数据资产',
            assetCount: 42,
            apiCount: 6,
            reportCount: 16,
            updateFreq: '每日',
            color: '#a29bfe',
            subDomains: ['组织架构', '绩效管理', '薪酬福利', '人才发展'],
            assetTypes: ['MySQL表', 'FineReport人事报表', '简道云HR应用', 'API接口'],
            platforms: ['MySQL', 'FineReport', '简道云']
        },
        {
            id: 'ts',
            name: '技术支持域',
            icon: 'fas fa-headset',
            description: '客户服务、技术支持、问题跟踪、服务质量等客服技术数据资产',
            assetCount: 48,
            apiCount: 11,
            reportCount: 14,
            updateFreq: '实时',
            color: '#ff6b6b',
            subDomains: ['客户服务', '技术支持', '问题管理', '服务质量'],
            assetTypes: ['PostgreSQL表', 'FineReport服务报表', 'FineBI服务分析', 'API接口'],
            platforms: ['PostgreSQL', 'FineReport', 'FineBI']
        },
        {
            id: 'mkt',
            name: '市场域',
            icon: 'fas fa-bullhorn',
            description: '市场活动、品牌推广、渠道管理、市场分析等市场营销数据资产',
            assetCount: 35,
            apiCount: 9,
            reportCount: 21,
            updateFreq: '每日',
            color: '#55a3ff',
            subDomains: ['市场活动', '品牌推广', '渠道管理', '竞品分析'],
            assetTypes: ['MySQL表', 'FineReport营销报表', 'FineBI市场分析', 'API接口'],
            platforms: ['MySQL', 'FineReport', 'FineBI']
        },
        {
            id: 'pub',
            name: '公共数据域',
            icon: 'fas fa-database',
            description: '字典信息、地理信息、日期维度、财务科目等公共基础数据资产',
            assetCount: 28,
            apiCount: 5,
            reportCount: 8,
            updateFreq: '按需',
            color: '#00d4aa',
            subDomains: ['字典维度', '地理信息', '时间维度', '基础配置'],
            assetTypes: ['MySQL维度表', 'PostgreSQL配置表', 'FineDataLink集成', 'API接口'],
            platforms: ['MySQL', 'PostgreSQL', 'FineDataLink']
        },
        {
            id: 'external',
            name: '外部数据域',
            icon: 'fas fa-cloud-download-alt',
            description: '第三方数据源、开放数据、合作伙伴数据等外部数据资产',
            assetCount: 67,
            apiCount: 23,
            reportCount: 12,
            updateFreq: '实时',
            color: '#74b9ff',
            subDomains: ['第三方API', '开放数据', '合作数据', '爬虫数据'],
            assetTypes: ['外部API', 'FineDataLink集成', '简道云数据', '第三方接口'],
            platforms: ['FineDataLink', '简道云', '第三方平台']
        }
    ],

    // 热门数据资产
    popularAssets: [
        {
            name: 'cust_company_info',
            domain: '客户域',
            type: 'MySQL表',
            platform: 'MySQL',
            description: '客户公司基础信息表，存储企业客户的核心信息',
            recordCount: '125,847',
            lastUpdate: '2小时前',
            usage: 'high',
            icon: 'fas fa-database'
        },
        {
            name: 'sales_performance_dashboard',
            domain: '销售域',
            type: 'FineBI仪表板',
            platform: 'FineBI',
            description: '销售业绩实时监控仪表板，展示关键销售指标',
            recordCount: '实时',
            lastUpdate: '实时',
            usage: 'high',
            icon: 'fas fa-chart-bar'
        },
        {
            name: 'financial_monthly_report',
            domain: '财务域',
            type: 'FineReport报表',
            platform: 'FineReport',
            description: '财务月度分析报表，包含收支、成本等核心财务指标',
            recordCount: '月度',
            lastUpdate: '1天前',
            usage: 'medium',
            icon: 'fas fa-chart-line'
        },
        {
            name: 'customer_service_data',
            domain: '技术支持域',
            type: 'PostgreSQL表',
            platform: 'PostgreSQL',
            description: '客户服务工单数据表，记录所有客服处理记录',
            recordCount: '45,678',
            lastUpdate: '30分钟前',
            usage: 'high',
            icon: 'fas fa-server'
        },
        {
            name: 'hr_management_app',
            domain: '人事域',
            type: '简道云应用',
            platform: '简道云',
            description: '人力资源管理应用，包含员工信息、考勤、绩效等功能',
            recordCount: '2,345',
            lastUpdate: '4小时前',
            usage: 'medium',
            icon: 'fas fa-cloud'
        },
        {
            name: 'data_integration_pipeline',
            domain: '公共数据域',
            type: 'FineDataLink管道',
            platform: 'FineDataLink',
            description: '核心数据集成管道，负责多源数据的ETL处理',
            recordCount: '管道',
            lastUpdate: '1小时前',
            usage: 'high',
            icon: 'fas fa-link'
        },
        {
            name: 'marketing_analysis_bi',
            domain: '市场域',
            type: 'FineBI分析',
            platform: 'FineBI',
            description: '市场营销效果分析，包含渠道转化、ROI等关键指标',
            recordCount: '分析模型',
            lastUpdate: '6小时前',
            usage: 'medium',
            icon: 'fas fa-chart-bar'
        },
        {
            name: 'operation_data_mysql',
            domain: '运营域',
            type: 'MySQL表',
            platform: 'MySQL',
            description: '运营活动数据表，记录用户行为和运营效果',
            recordCount: '892,456',
            lastUpdate: '1小时前',
            usage: 'high',
            icon: 'fas fa-database'
        }
    ],

    // 数据血缘关系配置
    dataLineage: {
        nodes: [
            // 数据源层
            { id: 'mysql', name: 'MySQL\n业务数据库', x: 80, y: 120, type: 'mysql', icon: 'fas fa-database' },
            { id: 'postgresql', name: 'PostgreSQL\n分析数据库', x: 80, y: 220, type: 'postgresql', icon: 'fas fa-server' },
            { id: 'jiandaoyun', name: '简道云\n业务应用', x: 80, y: 320, type: 'jiandaoyun', icon: 'fas fa-cloud' },

            // 数据集成层
            { id: 'finedatalink', name: 'FineDataLink\n数据集成平台', x: 280, y: 220, type: 'finedatalink', icon: 'fas fa-link' },

            // 数据仓库层
            { id: 'datawarehouse', name: '数据仓库\nODS/DWD/DWS', x: 480, y: 180, type: 'warehouse', icon: 'fas fa-warehouse' },
            { id: 'datamart', name: '数据集市\nADS层', x: 480, y: 280, type: 'datamart', icon: 'fas fa-cubes' },

            // 应用层
            { id: 'finereport', name: 'FineReport\n报表平台', x: 680, y: 120, type: 'finereport', icon: 'fas fa-chart-line' },
            { id: 'finebi', name: 'FineBI\n商业智能', x: 680, y: 220, type: 'finebi', icon: 'fas fa-chart-bar' },
            { id: 'api_services', name: 'API服务\n数据接口', x: 680, y: 320, type: 'api', icon: 'fas fa-plug' },

            // 终端用户层
            { id: 'business_users', name: '业务用户\n决策分析', x: 880, y: 180, type: 'users', icon: 'fas fa-users' },
            { id: 'external_apps', name: '外部应用\n系统集成', x: 880, y: 280, type: 'external', icon: 'fas fa-external-link-alt' }
        ],
        links: [
            // 数据源到集成层
            { source: 'mysql', target: 'finedatalink', label: '业务数据抽取', color: '#4285f4' },
            { source: 'postgresql', target: 'finedatalink', label: '分析数据同步', color: '#336791' },
            { source: 'jiandaoyun', target: 'finedatalink', label: '应用数据导入', color: '#00d4aa' },

            // 集成层到数据仓库
            { source: 'finedatalink', target: 'datawarehouse', label: 'ETL处理', color: '#ff6b6b' },
            { source: 'datawarehouse', target: 'datamart', label: '数据建模', color: '#a29bfe' },

            // 数据仓库到应用层
            { source: 'datawarehouse', target: 'finereport', label: '报表数据源', color: '#fd79a8' },
            { source: 'datamart', target: 'finebi', label: 'BI分析数据', color: '#fdcb6e' },
            { source: 'datamart', target: 'api_services', label: 'API数据服务', color: '#55a3ff' },

            // 直连数据源到应用
            { source: 'mysql', target: 'finereport', label: '实时报表', color: '#74b9ff' },
            { source: 'postgresql', target: 'finebi', label: '直连分析', color: '#81ecec' },

            // 应用层到用户层
            { source: 'finereport', target: 'business_users', label: '报表展示', color: '#fd79a8' },
            { source: 'finebi', target: 'business_users', label: 'BI仪表板', color: '#fdcb6e' },
            { source: 'api_services', target: 'external_apps', label: '数据接口', color: '#55a3ff' },

            // 简道云直接服务用户
            { source: 'jiandaoyun', target: 'business_users', label: '业务流程', color: '#00d4aa' }
        ]
    },

    // 数据质量指标
    qualityMetrics: {
        completeness: 95,
        timeliness: 88,
        accuracy: 92,
        consistency: 90
    },

    // 统计信息
    statistics: {
        totalAssets: 12991, // MySQL(7k) + PostgreSQL(3.4k) + FineReport(1.4k) + FineBI(1.1k) + 其他
        totalDomains: 9,
        totalApis: 89,
        totalReports: 1400, // 主要来自FineReport
        totalRecords: '15,678,234',
        dailyUpdates: '456,789'
    }
};



// 主题配置
const THEME_CONFIG = {
    colors: {
        primary: '#4285f4',
        secondary: '#fd79a8',
        accent: '#26de81',
        background: '#f8f9fa',
        surface: '#ffffff',
        text: '#212529',
        textSecondary: 'rgba(108, 117, 125, 0.8)',
        // 平台色彩
        mysql: '#4285f4',
        postgresql: '#336791',
        finereport: '#fd79a8',
        finebi: '#fdcb6e',
        finedatalink: '#ff6b6b',
        jiandaoyun: '#00d4aa'
    },

    animations: {
        duration: {
            fast: '0.2s',
            normal: '0.3s',
            slow: '0.5s'
        },
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DATA_CONFIG,
        THEME_CONFIG
    };
}
