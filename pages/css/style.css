/* 全局样式 - 升级为高端版本 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: radial-gradient(ellipse at top, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-attachment: fixed;
    color: #1a202c;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* 全局渐变动画背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(75, 0, 130, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(30, 144, 255, 0.08) 0%, transparent 50%);
    z-index: -2;
    animation: gradientShift 15s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.1); }
    50% { transform: rotate(180deg) scale(1); }
    75% { transform: rotate(270deg) scale(1.1); }
}

/* 高级粒子背景 */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    border-radius: 50%;
    filter: blur(0.5px);
    animation: float 8s infinite linear, glow 3s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(200px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes glow {
    0% { box-shadow: 0 0 10px rgba(255, 255, 255, 0.2); }
    100% { box-shadow: 0 0 30px rgba(255, 255, 255, 0.5); }
}

/* 高端脉冲动画 */
@keyframes pulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 主容器升级 */
.main-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    backdrop-filter: blur(1px);
}

/* 高端顶部导航 */
.header {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(255, 255, 255, 0.95) 50%, 
        rgba(255, 255, 255, 0.92) 100%);
    backdrop-filter: blur(20px) saturate(1.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1.2rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header:hover {
    box-shadow: 
        0 12px 48px rgba(0, 0, 0, 0.15),
        0 4px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1.5rem;
}

/* Logo升级 */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo:hover {
    transform: translateY(-2px);
}

.logo i {
    font-size: 2.2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoGlow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.3));
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

@keyframes logoGlow {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.05) rotate(5deg); }
}



/* 高级统计信息 */
.header-stats {
    display: flex;
    gap: 2.5rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.stat-item:hover {
    transform: translateY(-2px) scale(1.02);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-number {
    display: block;
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: countUp 2.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-label {
    font-size: 0.85rem;
    color: rgba(45, 55, 72, 0.8);
    font-weight: 600;
    margin-top: 0.2rem;
}

@keyframes countUp {
    from { 
        opacity: 0; 
        transform: translateY(20px) scale(0.8); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0) scale(1); 
    }
}

/* 主要内容升级 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem;
    position: relative;
}

/* 高端section标题 */
.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.75rem;
    margin-bottom: 2rem;
    color: #1a202c;
    font-weight: 700;
    position: relative;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: slideIn 0.6s ease-out;
}

.section-title i {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.3));
}

@keyframes slideIn {
    from { width: 0; }
    to { width: 60px; }
}



/* 数据资产分类升级 */
.asset-categories {
    margin-bottom: 4rem;
}

.categories-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.category-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 1px 4px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--category-color, #667eea) 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.category-item:hover::before {
    opacity: 0.05;
}

.category-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 
        0 12px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08);
}

.category-item.active {
    background: linear-gradient(135deg, var(--category-color, #667eea) 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 12px 32px rgba(0, 0, 0, 0.15),
        0 4px 16px var(--category-color, rgba(102, 126, 234, 0.3));
}

.category-icon {
    font-size: 2.5rem;
    color: var(--category-color, #667eea);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    display: block;
}

.category-item:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
}

.category-item.active .category-icon {
    color: white;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.category-name {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.category-item.active .category-name {
    color: white;
}

.category-count {
    font-size: 1.2rem;
    font-weight: 800;
    color: var(--category-color, #667eea);
}

.category-item.active .category-count {
    color: white;
}

/* 域卡片网格升级 */
.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 高端域卡片 */
.domain-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 24px;
    padding: 2.5rem;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.domain-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--domain-color, #667eea) 0%, #764ba2 50%, #f093fb 100%);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.domain-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, var(--domain-color, #667eea)10, transparent 50%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.domain-card:hover::before {
    transform: scaleX(1);
}

.domain-card:hover::after {
    opacity: 0.03;
}

.domain-card:hover {
    transform: translateY(-12px) scale(1.02) rotateX(2deg);
    box-shadow: 
        0 25px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.6);
}

/* 高端域卡片内部元素 */
.domain-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.domain-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1a202c;
    letter-spacing: -0.2px;
}

.domain-icon {
    font-size: 2.5rem;
    color: var(--domain-color, #667eea);
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.domain-card:hover .domain-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

.domain-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.domain-stat {
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    padding: 0.75rem 0.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.domain-stat:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.domain-stat-number {
    display: block;
    font-size: 1.3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--domain-color, #667eea) 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.domain-stat-label {
    font-size: 0.75rem;
    color: rgba(26, 32, 44, 0.7);
    margin-top: 0.3rem;
    font-weight: 600;
}

.domain-asset-types {
    margin-top: 1.5rem;
}

.asset-type-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.asset-type-tag {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%);
    color: var(--domain-color, #667eea);
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.asset-type-tag:hover {
    transform: translateY(-1px);
    background: linear-gradient(135deg, var(--domain-color, #667eea) 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.domain-description {
    color: rgba(26, 32, 44, 0.7);
    font-size: 0.95rem;
    line-height: 1.5;
    font-weight: 400;
}

/* 高端数据流图 */
.data-flow-section {
    margin-bottom: 4rem;
}

.lineage-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 24px;
    padding: 2rem;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 16px 48px rgba(0, 0, 0, 0.1),
        0 8px 24px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
}

.lineage-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    z-index: -1;
}

#lineage-svg {
    width: 100%;
    height: 500px;
    border-radius: 16px;
}

/* 高端血缘节点 */
.flow-node {
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(3px 3px 8px rgba(0, 0, 0, 0.15));
}

.flow-node:hover {
    filter: drop-shadow(6px 6px 16px rgba(0, 0, 0, 0.25));
    transform: scale(1.05);
}

/* 数据流动动画升级 */
.flow-link {
    fill: none;
    animation: flowAnimation 6s infinite linear;
    opacity: 0.9;
    stroke-width: 2;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.1));
}

.flow-label {
    text-anchor: middle;
    dominant-baseline: middle;
    font-weight: 600;
    pointer-events: none;
    font-size: 12px;
    fill: #1a202c;
    filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8));
}

/* 更流畅的流动动画 */
@keyframes flowAnimation {
    0% { 
        stroke-dasharray: 8, 8; 
        stroke-dashoffset: 0; 
        opacity: 0.6;
    }
    50% { 
        opacity: 1;
    }
    100% { 
        stroke-dasharray: 8, 8; 
        stroke-dashoffset: 16; 
        opacity: 0.6;
    }
}

.flow-text {
    fill: #1a202c;
    font-size: 11px;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
    font-weight: 600;
    filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8));
}

/* 高端血缘节点组 */
.flow-node-group {
    cursor: pointer;
    transition: all 0.3s ease;
}

.flow-node-group:hover .flow-node {
    stroke-width: 4;
    filter: drop-shadow(0 0 16px rgba(102, 126, 234, 0.4));
}

/* 增强流动粒子动画 */
.flow-particle {
    animation: particleFlow 4s infinite ease-in-out;
    filter: drop-shadow(0 0 6px rgba(102, 126, 234, 0.6));
}

@keyframes particleFlow {
    0%, 100% {
        opacity: 0;
        r: 1;
        fill: #667eea;
    }
    25% {
        opacity: 0.8;
        r: 2;
        fill: #764ba2;
    }
    50% {
        opacity: 1;
        r: 4;
        fill: #f093fb;
    }
    75% {
        opacity: 0.8;
        r: 2;
        fill: #764ba2;
    }
}

/* 高端热门数据资产 */
.assets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 4rem;
}

/* 高端资产卡片 */
.asset-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 20px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.asset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.03) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.asset-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.asset-card:hover::before {
    opacity: 1;
}

.asset-card:hover::after {
    transform: scaleX(1);
}

.asset-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 48px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 高端资产卡片内部元素 */
.asset-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.2rem;
}

.asset-icon {
    font-size: 2rem;
    color: #667eea;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.asset-card:hover .asset-icon {
    transform: scale(1.1) rotate(5deg);
    color: #764ba2;
}

.asset-type {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    color: #667eea;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: auto;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.asset-card:hover .asset-type {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.asset-name {
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    letter-spacing: -0.2px;
}

.asset-platform {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
    color: #667eea;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%);
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    width: fit-content;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s ease;
}

.asset-platform:hover {
    transform: translateY(-1px);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.asset-platform i {
    font-size: 0.9rem;
}

.asset-description {
    color: rgba(26, 32, 44, 0.7);
    font-size: 0.95rem;
    margin-bottom: 1.2rem;
    line-height: 1.5;
    font-weight: 400;
}

.asset-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: rgba(26, 32, 44, 0.6);
}

.asset-domain {
    color: #667eea;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.4) 100%);
    padding: 0.3rem 0.6rem;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.asset-usage {
    padding: 0.3rem 0.7rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.asset-usage:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.asset-usage.high {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 53, 69, 0.1) 100%);
    color: #dc3545;
}

.asset-usage.medium {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
    color: #f59e0b;
}

.asset-usage.low {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(25, 135, 84, 0.1) 100%);
    color: #22c55e;
}

/* 高端数据质量监控 */
.quality-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

/* 高端质量卡片 */
.quality-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 24px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quality-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.quality-card:hover::before {
    opacity: 1;
}

.quality-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 高端质量卡片内部元素 */
.quality-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.quality-card:hover .quality-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.3));
}

.quality-info h3 {
    margin-bottom: 0.75rem;
    color: #1a202c;
    font-size: 1.1rem;
    font-weight: 700;
    letter-spacing: -0.2px;
}

.progress-bar {
    width: 220px;
    height: 12px;
    background: linear-gradient(135deg, rgba(243, 244, 246, 0.8) 0%, rgba(229, 231, 235, 0.6) 100%);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 0.75rem;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 12px;
    width: 0;
    animation: progressAnimation 2.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressAnimation {
    to { width: var(--progress); }
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-weight: 800;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #ffffff;
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 15px;
    width: 80%;
    max-width: 800px;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #495057;
}

/* 高端加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 50%, rgba(240, 147, 251, 0.95) 100%);
    backdrop-filter: blur(20px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.8s ease-out;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 2rem;
    opacity: 0.9;
    animation: fadeInOut 2s ease-in-out infinite;
}

.spinner {
    width: 80px;
    height: 80px;
    position: relative;
    margin: 0 auto;
}

.spinner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
}

.spinner::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border: 4px solid transparent;
    border-bottom: 4px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: spin 1s linear infinite reverse;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* 额外动画效果 */
.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(64, 224, 255, 0.2);
    }
    to {
        box-shadow: 0 0 20px rgba(64, 224, 255, 0.6), 0 0 30px rgba(64, 224, 255, 0.4);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据流动画增强 */
.flow-particle {
    fill: #40e0ff;
    r: 3;
    animation: flowParticle 4s infinite linear;
}

@keyframes flowParticle {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

/* 悬停效果增强 */
.domain-card:hover .domain-icon {
    animation: iconSpin 0.5s ease-in-out;
}

@keyframes iconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.table-card:hover {
    background: linear-gradient(135deg, rgba(64, 224, 255, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
}



/* 模态框内容样式 */
.domain-detail-stats {
    display: flex;
    justify-content: space-around;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.domain-detail-stats .stat-item {
    text-align: center;
}

.domain-detail-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.domain-detail-stats .stat-label {
    font-size: 0.9rem;
    color: rgba(108, 117, 125, 0.8);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(233, 236, 239, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #495057, #6c757d);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #6c757d, #495057);
}

/* 资产详情样式 */
.asset-detail-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.asset-type-badge, .asset-domain-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-type-badge {
    background: rgba(248, 249, 250, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-domain-badge {
    background: rgba(233, 236, 239, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-platform-badge {
    background: rgba(220, 248, 198, 0.8);
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.3);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.usage-high {
    color: #dc3545 !important;
}

.usage-medium {
    color: #ffc107 !important;
}

.usage-low {
    color: #198754 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-stats {
        gap: 1rem;
        flex-wrap: wrap;
    }



    .categories-nav {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .main-content {
        padding: 1rem;
    }

    .domains-grid {
        grid-template-columns: 1fr;
    }

    .assets-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }

    .quality-dashboard {
        grid-template-columns: 1fr;
    }

    .domain-detail-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .asset-detail-stats {
        grid-template-columns: 1fr;
    }

    #lineage-svg {
        height: 400px;
    }
}
